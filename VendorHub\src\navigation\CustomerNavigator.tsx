import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { useTheme, useCart, useI18n } from '../hooks';
import { StyleSheet } from 'react-native';
import { RTLIcon, RTLText, RTLView } from '../components/RTL';
import { COLORS, FONT_SIZES, FONT_WEIGHTS } from '../constants/theme';

import { HomeScreen } from '../screens/public/HomeScreen';
import { ShopsScreen } from '../screens/public/ShopsScreen';
import { CartScreen } from '../screens/public/CartScreen';
import { ProductDetailsScreen } from '../screens/public/ProductDetailsScreen';
import { CheckoutScreen } from '../screens/public/CheckoutScreen';
import { VendorShopScreen } from '../screens/public/VendorShopScreen';
import { SearchScreen } from '../screens/public/SearchScreen';
import { ProfileScreen } from '../screens/public/ProfileScreen';
import { OrderHistoryScreen } from '../screens/public/OrderHistoryScreen';
import { OrderDetailsScreen } from '../screens/public/OrderDetailsScreen';
import { ChatListScreen } from '../screens/chat/ChatListScreen';
import { ChatScreen } from '../screens/chat/ChatScreen';

export type CustomerTabParamList = {
  Home: undefined;
  Shops: undefined;
  Cart: undefined;
  Messages: undefined;
  Profile: undefined;
};

export type CustomerStackParamList = {
  CustomerTabs: undefined;
  ProductDetails: { productId: string };
  VendorShop: { vendorId: string };
  Checkout: undefined;
  OrderHistory: undefined;
  OrderDetails: { orderId: string };
  Search: undefined;
  Chat: { chatId: string; vendorId?: string; vendorName?: string };
};

const Tab = createBottomTabNavigator<CustomerTabParamList>();
const Stack = createStackNavigator<CustomerStackParamList>();

// Cart Badge Component
const CartBadge: React.FC<{ count: number }> = ({ count }) => {
  const { isRTL } = useI18n();

  if (count === 0) return null;

  return (
    <RTLView style={[styles.badge, isRTL && styles.badgeRTL].filter(Boolean) as any}>
      <RTLText style={styles.badgeText}>
        {count > 99 ? '99+' : count.toString()}
      </RTLText>
    </RTLView>
  );
};

const CustomerTabs: React.FC = () => {
  const { colors } = useTheme();
  const { cartItemCount } = useCart();
  const { t, isRTL } = useI18n();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Shops':
              iconName = focused ? 'storefront' : 'storefront-outline';
              break;
            case 'Cart':
              iconName = focused ? 'bag' : 'bag-outline';
              break;
            case 'Messages':
              iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
              break;
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'ellipse-outline';
          }

          const icon = <RTLIcon name={iconName as any} size={size} color={color} />;

          // Add badge to cart icon
          if (route.name === 'Cart' && cartItemCount > 0) {
            return (
              <RTLView>
                {icon}
                <CartBadge count={cartItemCount} />
              </RTLView>
            );
          }

          return icon;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarShowLabel: false,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerStyle: {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        headerTintColor: colors.textPrimary,
        headerTitleStyle: {
          fontWeight: '600',
        },
      })}
    >
      {isRTL ? (
        // RTL order: Profile, Messages, Cart, Shops, Home (so Home appears first from right)
        <>
          <Tab.Screen
            name="Profile"
            component={ProfileScreen}
            options={{ title: t('nav.profile') }}
          />
          <Tab.Screen
            name="Messages"
            component={ChatListScreen}
            options={{ title: t('nav.messages') }}
          />
          <Tab.Screen
            name="Cart"
            component={CartScreen}
            options={{
              title: t('nav.cart'),
              tabBarBadge: cartItemCount > 0 ? cartItemCount : undefined,
            }}
          />
          <Tab.Screen
            name="Shops"
            component={ShopsScreen}
            options={{ title: t('nav.shops') }}
          />
          <Tab.Screen
            name="Home"
            component={HomeScreen}
            options={{ title: t('nav.home') }}
          />
        </>
      ) : (
        // LTR order: Home, Shops, Cart, Messages, Profile (normal left-to-right)
        <>
          <Tab.Screen
            name="Home"
            component={HomeScreen}
            options={{ title: t('nav.home') }}
          />
          <Tab.Screen
            name="Shops"
            component={ShopsScreen}
            options={{ title: t('nav.shops') }}
          />
          <Tab.Screen
            name="Cart"
            component={CartScreen}
            options={{
              title: t('nav.cart'),
              tabBarBadge: cartItemCount > 0 ? cartItemCount : undefined,
            }}
          />
          <Tab.Screen
            name="Messages"
            component={ChatListScreen}
            options={{ title: t('nav.messages') }}
          />
          <Tab.Screen
            name="Profile"
            component={ProfileScreen}
            options={{ title: t('nav.profile') }}
          />
        </>
      )}
    </Tab.Navigator>
  );
};

export const CustomerNavigator: React.FC = () => {
  const { colors } = useTheme();
  const { t, isRTL } = useI18n();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        headerTintColor: colors.textPrimary,
        headerTitleStyle: {
          fontWeight: '600',
        },
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [
                      isRTL ? -layouts.screen.width : layouts.screen.width,
                      0
                    ],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen
        name="CustomerTabs"
        component={CustomerTabs}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ProductDetails"
        component={ProductDetailsScreen}
        options={{
          title: t('nav.productDetails'),
        }}
      />
      <Stack.Screen
        name="VendorShop"
        component={VendorShopScreen}
        options={{
          title: t('nav.shop'),
        }}
      />
      <Stack.Screen
        name="Checkout"
        component={CheckoutScreen}
        options={{
          title: t('nav.checkout'),
        }}
      />
      <Stack.Screen
        name="OrderHistory"
        component={OrderHistoryScreen}
        options={{
          title: t('nav.orderHistory'),
        }}
      />
      <Stack.Screen
        name="OrderDetails"
        component={OrderDetailsScreen}
        options={{
          title: t('nav.orderDetails'),
        }}
      />
      <Stack.Screen
        name="Search"
        component={SearchScreen}
        options={{
          title: t('nav.search'),
        }}
      />
      <Stack.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          title: t('nav.chat'),
        }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  badge: {
    position: 'absolute',
    right: -8,
    top: -4,
    backgroundColor: COLORS.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeRTL: {
    right: undefined,
    left: -8,
  },
  badgeText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    textAlign: 'center',
  },
});
