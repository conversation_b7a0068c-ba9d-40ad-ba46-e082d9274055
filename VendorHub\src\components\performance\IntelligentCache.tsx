import React, { createContext, useContext, useRef, useCallback, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, Platform } from 'react-native';

// Conditionally import NetInfo for native platforms
let NetInfo: any = null;
try {
  if (Platform.OS !== 'web') {
    const netInfoModule = require('@react-native-community/netinfo');
    NetInfo = netInfoModule.default || netInfoModule;
  }
} catch (error) {
  console.warn('NetInfo not available:', error);
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  size: number;
  tags: string[];
}

interface CacheConfig {
  maxMemorySize: number; // in MB
  maxDiskSize: number; // in MB
  defaultTTL: number; // in milliseconds
  cleanupInterval: number; // in milliseconds
  compressionThreshold: number; // in bytes
  enablePersistence: boolean;
  enableCompression: boolean;
  enableMetrics: boolean;
}

interface CacheMetrics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  memoryUsage: number;
  diskUsage: number;
  evictionCount: number;
  compressionRatio: number;
}

interface IntelligentCacheContextType {
  get: <T>(key: string) => Promise<T | null>;
  set: <T>(key: string, data: T, options?: CacheOptions) => Promise<void>;
  remove: (key: string) => Promise<void>;
  clear: () => Promise<void>;
  invalidateByTag: (tag: string) => Promise<void>;
  prefetch: <T>(key: string, fetcher: () => Promise<T>, options?: CacheOptions) => Promise<void>;
  getMetrics: () => CacheMetrics;
  warmup: (keys: string[]) => Promise<void>;
  optimize: () => Promise<void>;
}

interface CacheOptions {
  ttl?: number;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  tags?: string[];
  compress?: boolean;
  persist?: boolean;
}

const DEFAULT_CONFIG: CacheConfig = {
  maxMemorySize: 50, // 50MB
  maxDiskSize: 200, // 200MB
  defaultTTL: 30 * 60 * 1000, // 30 minutes
  cleanupInterval: 5 * 60 * 1000, // 5 minutes
  compressionThreshold: 1024, // 1KB
  enablePersistence: true,
  enableCompression: true,
  enableMetrics: true,
};

const IntelligentCacheContext = createContext<IntelligentCacheContextType | undefined>(undefined);

export const useIntelligentCache = (): IntelligentCacheContextType => {
  const context = useContext(IntelligentCacheContext);
  if (!context) {
    throw new Error('useIntelligentCache must be used within an IntelligentCacheProvider');
  }
  return context;
};

interface IntelligentCacheProviderProps {
  children: React.ReactNode;
  config?: Partial<CacheConfig>;
}

export const IntelligentCacheProvider: React.FC<IntelligentCacheProviderProps> = ({
  children,
  config = {},
}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Memory cache
  const memoryCache = useRef<Map<string, CacheEntry<any>>>(new Map());
  
  // Cache metrics
  const metrics = useRef<CacheMetrics>({
    hitRate: 0,
    missRate: 0,
    totalRequests: 0,
    totalHits: 0,
    totalMisses: 0,
    memoryUsage: 0,
    diskUsage: 0,
    evictionCount: 0,
    compressionRatio: 1,
  });

  // Cleanup timer
  const cleanupTimer = useRef<NodeJS.Timeout>();
  
  // Network state
  const isOnline = useRef(true);

  // Initialize cache
  useEffect(() => {
    initializeCache();
    startCleanupTimer();
    const networkUnsubscribe = setupNetworkListener();
    const appStateUnsubscribe = setupAppStateListener();

    return () => {
      if (cleanupTimer.current) {
        clearInterval(cleanupTimer.current);
      }
      if (networkUnsubscribe) {
        networkUnsubscribe();
      }
      if (appStateUnsubscribe) {
        appStateUnsubscribe();
      }
    };
  }, []);

  const initializeCache = async () => {
    try {
      if (finalConfig.enablePersistence) {
        // Load persistent cache entries
        const persistentKeys = await AsyncStorage.getAllKeys();
        const cacheKeys = persistentKeys.filter(key => key.startsWith('cache:'));
        
        for (const key of cacheKeys.slice(0, 100)) { // Limit initial load
          try {
            const data = await AsyncStorage.getItem(key);
            if (data) {
              const entry = JSON.parse(data);
              const cacheKey = key.replace('cache:', '');
              
              // Check if entry is still valid
              if (entry.expiresAt > Date.now()) {
                memoryCache.current.set(cacheKey, entry);
              } else {
                // Remove expired entry
                await AsyncStorage.removeItem(key);
              }
            }
          } catch (error) {
            console.warn('Failed to load cache entry:', key, error);
            await AsyncStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to initialize cache:', error);
    }
  };

  const startCleanupTimer = () => {
    cleanupTimer.current = setInterval(() => {
      performCleanup();
    }, finalConfig.cleanupInterval);
  };

  const setupNetworkListener = () => {
    if (!NetInfo) {
      // For web or when NetInfo is not available, assume online
      isOnline.current = true;
      return () => {}; // Return empty cleanup function
    }

    try {
      const unsubscribe = NetInfo.addEventListener(state => {
        isOnline.current = state.isConnected ?? true;
      });
      return unsubscribe;
    } catch (error) {
      console.warn('Failed to setup network listener:', error);
      isOnline.current = true;
      return () => {}; // Return empty cleanup function
    }
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background') {
        // Perform cleanup when app goes to background
        performCleanup();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  };

  const calculateSize = (data: any): number => {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate in bytes
    } catch {
      return 0;
    }
  };

  const compressData = (data: any): string => {
    if (!finalConfig.enableCompression) {
      return JSON.stringify(data);
    }
    
    // Simple compression (in production, use a proper compression library)
    const jsonString = JSON.stringify(data);
    if (jsonString.length < finalConfig.compressionThreshold) {
      return jsonString;
    }
    
    // Placeholder for actual compression
    return jsonString;
  };

  const decompressData = (compressedData: string): any => {
    try {
      return JSON.parse(compressedData);
    } catch {
      return null;
    }
  };

  const updateMetrics = (hit: boolean) => {
    if (!finalConfig.enableMetrics) return;
    
    metrics.current.totalRequests++;
    if (hit) {
      metrics.current.totalHits++;
    } else {
      metrics.current.totalMisses++;
    }
    
    metrics.current.hitRate = metrics.current.totalHits / metrics.current.totalRequests;
    metrics.current.missRate = metrics.current.totalMisses / metrics.current.totalRequests;
  };

  const evictLRU = () => {
    const entries = Array.from(memoryCache.current.entries());
    
    // Sort by priority and last accessed time
    entries.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a[1].priority];
      const bPriority = priorityOrder[b[1].priority];
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority; // Lower priority first
      }
      
      return a[1].lastAccessed - b[1].lastAccessed; // Older first
    });
    
    // Remove the least important entries
    const toRemove = Math.ceil(entries.length * 0.2); // Remove 20%
    for (let i = 0; i < toRemove; i++) {
      const [key] = entries[i];
      memoryCache.current.delete(key);
      metrics.current.evictionCount++;
    }
  };

  const performCleanup = async () => {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    // Find expired entries
    for (const [key, entry] of memoryCache.current.entries()) {
      if (entry.expiresAt <= now) {
        expiredKeys.push(key);
      }
    }
    
    // Remove expired entries
    for (const key of expiredKeys) {
      memoryCache.current.delete(key);
      if (finalConfig.enablePersistence) {
        try {
          await AsyncStorage.removeItem(`cache:${key}`);
        } catch (error) {
          console.warn('Failed to remove expired cache entry:', key, error);
        }
      }
    }
    
    // Check memory usage and evict if necessary
    const currentMemoryUsage = Array.from(memoryCache.current.values())
      .reduce((total, entry) => total + entry.size, 0);
    
    metrics.current.memoryUsage = currentMemoryUsage / (1024 * 1024); // Convert to MB
    
    if (metrics.current.memoryUsage > finalConfig.maxMemorySize) {
      evictLRU();
    }
  };

  const get = useCallback(async <T,>(key: string): Promise<T | null> => {
    try {
      // Check memory cache first
      const memoryEntry = memoryCache.current.get(key);
      if (memoryEntry && memoryEntry.expiresAt > Date.now()) {
        memoryEntry.lastAccessed = Date.now();
        memoryEntry.accessCount++;
        updateMetrics(true);
        return memoryEntry.data as T;
      }
      
      // Check persistent cache
      if (finalConfig.enablePersistence) {
        const persistentData = await AsyncStorage.getItem(`cache:${key}`);
        if (persistentData) {
          const entry = JSON.parse(persistentData);
          if (entry.expiresAt > Date.now()) {
            // Move to memory cache
            entry.lastAccessed = Date.now();
            entry.accessCount++;
            memoryCache.current.set(key, entry);
            updateMetrics(true);
            return entry.data as T;
          } else {
            // Remove expired entry
            await AsyncStorage.removeItem(`cache:${key}`);
          }
        }
      }
      
      updateMetrics(false);
      return null;
    } catch (error) {
      console.warn('Cache get error:', error);
      updateMetrics(false);
      return null;
    }
  }, [finalConfig.enablePersistence]);

  const set = useCallback(async <T,>(
    key: string,
    data: T,
    options: CacheOptions = {}
  ): Promise<void> => {
    try {
      const now = Date.now();
      const ttl = options.ttl || finalConfig.defaultTTL;
      const priority = options.priority || 'medium';
      const tags = options.tags || [];
      const size = calculateSize(data);
      
      const entry: CacheEntry<T> = {
        data,
        timestamp: now,
        expiresAt: now + ttl,
        accessCount: 1,
        lastAccessed: now,
        priority,
        size,
        tags,
      };
      
      // Store in memory cache
      memoryCache.current.set(key, entry);
      
      // Store in persistent cache if enabled
      if (finalConfig.enablePersistence && (options.persist !== false)) {
        const compressedData = compressData(entry);
        await AsyncStorage.setItem(`cache:${key}`, compressedData);
      }
    } catch (error) {
      console.warn('Cache set error:', error);
    }
  }, [finalConfig.defaultTTL, finalConfig.enablePersistence]);

  const remove = useCallback(async (key: string): Promise<void> => {
    try {
      memoryCache.current.delete(key);
      if (finalConfig.enablePersistence) {
        await AsyncStorage.removeItem(`cache:${key}`);
      }
    } catch (error) {
      console.warn('Cache remove error:', error);
    }
  }, [finalConfig.enablePersistence]);

  const clear = useCallback(async (): Promise<void> => {
    try {
      memoryCache.current.clear();
      if (finalConfig.enablePersistence) {
        const keys = await AsyncStorage.getAllKeys();
        const cacheKeys = keys.filter(key => key.startsWith('cache:'));
        await AsyncStorage.multiRemove(cacheKeys);
      }
      
      // Reset metrics
      metrics.current = {
        hitRate: 0,
        missRate: 0,
        totalRequests: 0,
        totalHits: 0,
        totalMisses: 0,
        memoryUsage: 0,
        diskUsage: 0,
        evictionCount: 0,
        compressionRatio: 1,
      };
    } catch (error) {
      console.warn('Cache clear error:', error);
    }
  }, [finalConfig.enablePersistence]);

  const invalidateByTag = useCallback(async (tag: string): Promise<void> => {
    try {
      const keysToRemove: string[] = [];
      
      for (const [key, entry] of memoryCache.current.entries()) {
        if (entry.tags.includes(tag)) {
          keysToRemove.push(key);
        }
      }
      
      for (const key of keysToRemove) {
        await remove(key);
      }
    } catch (error) {
      console.warn('Cache invalidateByTag error:', error);
    }
  }, [remove]);

  const prefetch = useCallback(async <T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<void> => {
    try {
      // Check if already cached
      const cached = await get<T>(key);
      if (cached !== null) {
        return;
      }
      
      // Fetch and cache
      const data = await fetcher();
      await set(key, data, options);
    } catch (error) {
      console.warn('Cache prefetch error:', error);
    }
  }, [get, set]);

  const getMetrics = useCallback((): CacheMetrics => {
    return { ...metrics.current };
  }, []);

  const warmup = useCallback(async (keys: string[]): Promise<void> => {
    // Placeholder for cache warmup logic
    console.log('Cache warmup for keys:', keys);
  }, []);

  const optimize = useCallback(async (): Promise<void> => {
    await performCleanup();
    // Additional optimization logic can be added here
  }, []);

  const contextValue: IntelligentCacheContextType = {
    get,
    set,
    remove,
    clear,
    invalidateByTag,
    prefetch,
    getMetrics,
    warmup,
    optimize,
  };

  return (
    <IntelligentCacheContext.Provider value={contextValue}>
      {children}
    </IntelligentCacheContext.Provider>
  );
};
